"""Database models for the gemfinder application."""

from sqlalchemy import <PERSON>umn, Integer, String, BigInteger, DECIMAL, Index, ForeignKey
from sqlalchemy.dialects.postgresql import TIMESTAMP
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field
from app.database import Base


class Token(Base):
    """Model for storing token metadata."""

    __tablename__ = "tokens"

    mint_address = Column(String, primary_key=True)  # Token mint address
    name = Column(String, nullable=True)  # Token name
    symbol = Column(String, nullable=True)  # Token symbol
    decimals = Column(Integer, default=6, nullable=False)  # Token decimals
    created_at = Column(TIMESTAMP(timezone=True), default=func.now(), nullable=False)
    updated_at = Column(TIMESTAMP(timezone=True), default=func.now(), onupdate=func.now(), nullable=False)

    # Relationship to swaps
    swaps = relationship("Swap", back_populates="token")


class Swap(Base):
    """Model for storing pump.fun swap transactions."""

    __tablename__ = "swaps"

    id = Column(Integer, primary_key=True)
    timestamp = Column(TIMESTAMP(timezone=True), primary_key=True, default=func.now(), nullable=False, index=True)
    signature = Column(String, nullable=False, index=True, unique=True)
    mint_address = Column(String, ForeignKey('tokens.mint_address'), nullable=False, index=True)
    token_amount = Column(BigInteger, nullable=False)  # Token amount in smallest unit
    sol_amount = Column(BigInteger, nullable=False)  # SOL amount in lamports
    price_per_token = Column(DECIMAL(20, 10), nullable=False)  # Price per token in SOL
    swap_type = Column(String, nullable=False, index=True)  # 'buy' or 'sell'
    user_wallet = Column(String, nullable=False, index=True)

    # Relationship to token
    token = relationship("Token", back_populates="swaps")

    # Add composite indexes for better query performance
    __table_args__ = (
        Index('idx_swaps_mint_timestamp', 'mint_address', 'timestamp'),
        Index('idx_swaps_user_timestamp', 'user_wallet', 'timestamp'),
        Index('idx_swaps_type_timestamp', 'swap_type', 'timestamp'),
    )


# Pydantic models for API serialization
class TokenBase(BaseModel):
    """Base token model for API responses."""

    mint_address: str = Field(..., description="Token mint address")
    name: Optional[str] = Field(None, description="Token name")
    symbol: Optional[str] = Field(None, description="Token symbol")
    decimals: int = Field(6, description="Token decimals")

    class Config:
        from_attributes = True


class TokenCreate(BaseModel):
    """Model for creating new token records."""

    mint_address: str
    name: Optional[str] = None
    symbol: Optional[str] = None
    decimals: int = 6


class SwapBase(BaseModel):
    """Base swap model for API responses."""

    signature: str = Field(..., description="Transaction signature")
    mint_address: str = Field(..., description="Token mint address")
    token_amount: int = Field(..., description="Token amount in smallest unit")
    sol_amount: int = Field(..., description="SOL amount in lamports")
    price_per_token: float = Field(..., description="Price per token in SOL")
    swap_type: str = Field(..., description="Swap type: buy or sell")
    user_wallet: str = Field(..., description="User wallet address")

    class Config:
        from_attributes = True


class SwapCreate(BaseModel):
    """Model for creating new swap records."""

    signature: str
    mint_address: str
    token_amount: int
    sol_amount: int
    price_per_token: float
    swap_type: str
    user_wallet: str


class SwapResponse(SwapBase):
    """Extended swap model with additional fields for API responses."""

    id: int
    timestamp: datetime
    seconds_ago: Optional[float] = Field(None, description="Seconds since swap occurred")

    # Token information (when joined)
    token_name: Optional[str] = Field(None, description="Token name")
    token_symbol: Optional[str] = Field(None, description="Token symbol")
    token_decimals: int = Field(6, description="Token decimals")

    # Computed display values
    sol_display: float = Field(..., description="SOL amount for display (9 decimals)")
    token_display: float = Field(..., description="Token amount for display (6 decimals)")


class TokenStats(BaseModel):
    """Model for token statistics."""

    mint_address: str
    token_name: Optional[str] = None
    token_symbol: Optional[str] = None
    total_swaps: int
    buy_count: int
    sell_count: int
    total_sol_bought: float = 0.0
    total_sol_sold: float = 0.0
    avg_price: float = 0.0
    max_price: float = 0.0
    min_price: float = 0.0
    last_swap_time: Optional[datetime] = None

    class Config:
        from_attributes = True


class VolumeAnomaly(BaseModel):
    """Model for volume anomaly detection results."""

    mint_address: str
    token_name: Optional[str] = None
    token_symbol: Optional[str] = None

    # Volume metrics
    volume_1h: float = 0.0  # SOL volume in last 1 hour
    volume_6h: float = 0.0  # SOL volume in last 6 hours
    volume_24h: float = 0.0  # SOL volume in last 24 hours

    # Historical averages for comparison
    avg_volume_1h: float = 0.0  # Average 1h volume over last 7 days
    avg_volume_6h: float = 0.0  # Average 6h volume over last 7 days
    avg_volume_24h: float = 0.0  # Average 24h volume over last 7 days

    # Anomaly scores (higher = more anomalous)
    anomaly_score_1h: float = 0.0  # Current 1h volume / avg 1h volume
    anomaly_score_6h: float = 0.0  # Current 6h volume / avg 6h volume
    anomaly_score_24h: float = 0.0  # Current 24h volume / avg 24h volume

    # Overall anomaly score (weighted combination)
    overall_anomaly_score: float = 0.0

    # Additional metrics
    swap_count_1h: int = 0
    swap_count_6h: int = 0
    swap_count_24h: int = 0

    largest_swap_1h: float = 0.0  # Largest single swap in last hour
    last_activity: Optional[datetime] = None

    class Config:
        from_attributes = True


class LargeSwap(BaseModel):
    """Model for large/significant swaps."""

    signature: str
    mint_address: str
    token_name: Optional[str] = None
    token_symbol: Optional[str] = None
    token_decimals: int = 6

    timestamp: datetime
    sol_amount: int  # In lamports
    token_amount: int  # In smallest unit
    sol_display: float  # SOL amount for display
    token_display: float  # Token amount for display
    price_per_token: float
    swap_type: str
    user_wallet: str

    # Significance metrics
    is_whale_trade: bool = False  # > 10 SOL
    is_large_trade: bool = False  # > 1 SOL

    seconds_ago: Optional[float] = None

    class Config:
        from_attributes = True
