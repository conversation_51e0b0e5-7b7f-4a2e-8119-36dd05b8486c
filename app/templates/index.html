<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gemfinder - Solana Pump.fun Tracker</title>
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="/static/style.css">
</head>

<body class="bg-gray-900 text-white min-h-screen">
    <!-- Header -->
    <header class="bg-gray-800 shadow-lg">
        <div class="container mx-auto px-4 py-6">
            <div class="flex items-center justify-between">
                <h1 class="text-3xl font-bold text-purple-400">💎 Gemfinder</h1>
                <p class="text-gray-300">Real-time Pump.fun Swap Tracker</p>
            </div>
        </div>
    </header>

    <!-- Stats Dashboard -->
    <section class="container mx-auto px-4 py-8">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-gray-800 rounded-lg p-6 text-center">
                <h3 class="text-lg font-semibold text-gray-300 mb-2">Total Swaps</h3>
                <p class="text-3xl font-bold text-green-400" id="total-swaps">{{ total_swaps }}</p>
            </div>
            <div class="bg-gray-800 rounded-lg p-6 text-center">
                <h3 class="text-lg font-semibold text-gray-300 mb-2">Unique Tokens</h3>
                <p class="text-3xl font-bold text-blue-400" id="unique-tokens">{{ unique_tokens }}</p>
            </div>
            <div class="bg-gray-800 rounded-lg p-6 text-center">
                <h3 class="text-lg font-semibold text-gray-300 mb-2">Large Trades (1h)</h3>
                <p class="text-3xl font-bold text-orange-400" id="large-swaps-count">{{ large_swaps_count }}</p>
            </div>
            <div class="bg-gray-800 rounded-lg p-6 text-center">
                <h3 class="text-lg font-semibold text-gray-300 mb-2">Status</h3>
                <p class="text-3xl font-bold text-green-400">🟢 Live</p>
            </div>
        </div>

        <!-- Auto-refresh stats every 30 seconds -->
        <div hx-get="/api/stats" hx-trigger="every 30s" hx-target="#stats-container" hx-swap="none"
            style="display: none;"></div>
    </section>

    <!-- Volume Anomalies -->
    <section class="container mx-auto px-4 pb-8">
        <div class="bg-gray-800 rounded-lg shadow-lg overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-700">
                <h2 class="text-xl font-semibold text-white flex items-center">
                    <span class="mr-2">🚀</span>
                    Volume Anomalies & Large Trades
                    <span class="ml-auto text-sm text-gray-400">Auto-refreshing every 10s</span>
                </h2>
                <p class="text-sm text-gray-400 mt-1">
                    Tokens with unusual volume spikes and their recent large trades (>1 SOL)
                </p>
            </div>

            <div id="anomalies-container" class="p-6" hx-get="/api/anomalies/recent" hx-trigger="every 10s"
                hx-swap="innerHTML">
                {% for item in anomaly_data %}
                <div class="bg-gray-800 rounded-lg p-4 mb-4 border border-gray-700">
                    <div class="flex justify-between items-center mb-3">
                        <div class="flex items-center space-x-3">
                            <span class="text-lg font-bold text-purple-400">
                                {% if item.anomaly.token_symbol %}
                                {{ item.anomaly.token_symbol }}
                                {% elif item.anomaly.token_name %}
                                {{ item.anomaly.token_name }}
                                {% else %}
                                {{ item.anomaly.mint_address[:8] }}...{{ item.anomaly.mint_address[-8:] }}
                                {% endif %}
                            </span>
                            <span
                                class="{% if item.anomaly.overall_anomaly_score >= 10 %}text-red-400{% elif item.anomaly.overall_anomaly_score >= 5 %}text-orange-400{% else %}text-yellow-400{% endif %} font-bold">
                                {% if item.anomaly.overall_anomaly_score >= 10 %}🔥{% elif
                                item.anomaly.overall_anomaly_score >= 5 %}⚡{% else %}📈{% endif %} {{
                                "%.1f"|format(item.anomaly.overall_anomaly_score) }}x
                            </span>
                        </div>
                        <div class="text-sm text-gray-400">
                            {{ item.anomaly.swap_count_1h }} swaps (1h)
                        </div>
                    </div>

                    <div class="grid grid-cols-3 gap-4 mb-3 text-sm">
                        <div>
                            <span class="text-gray-400">1h Volume:</span>
                            <span class="text-yellow-400 font-bold">{{ "%.3f"|format(item.anomaly.volume_1h) }}
                                SOL</span>
                        </div>
                        <div>
                            <span class="text-gray-400">6h Volume:</span>
                            <span class="text-yellow-400">{{ "%.3f"|format(item.anomaly.volume_6h) }} SOL</span>
                        </div>
                        <div>
                            <span class="text-gray-400">24h Volume:</span>
                            <span class="text-yellow-400">{{ "%.3f"|format(item.anomaly.volume_24h) }} SOL</span>
                        </div>
                    </div>

                    {% if item.large_swaps %}
                    <div class="border-t border-gray-700 pt-3">
                        <h4 class="text-sm font-semibold text-gray-300 mb-2">Recent Large Trades:</h4>
                        <div class="space-y-1">
                            {% for swap in item.large_swaps %}
                            <div class="flex justify-between items-center text-xs bg-gray-900 rounded p-2">
                                <div class="flex items-center space-x-2">
                                    <span
                                        class="{% if swap.swap_type == 'buy' %}text-green-400{% else %}text-red-400{% endif %}">
                                        {% if swap.swap_type == 'buy' %}🟢{% else %}🔴{% endif %} {{
                                        swap.swap_type.title() }}
                                    </span>
                                    <span class="text-yellow-400 font-bold">{{ "%.3f"|format(swap.sol_display) }}
                                        SOL</span>
                                    <span class="text-blue-400">{{ "%.2f"|format(swap.token_display) }}</span>
                                    {% if swap.is_whale_trade %}🐋{% endif %}
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="text-gray-400">
                                        {% if swap.seconds_ago < 60 %}{{ swap.seconds_ago|int }}s ago{% else %}{{
                                            (swap.seconds_ago/60)|int }}m ago{% endif %} </span>
                                            <a href="https://solscan.io/tx/{{ swap.signature }}" target="_blank"
                                                class="text-blue-400 hover:text-blue-300">→</a>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                </div>
                {% endfor %}

                {% if not anomaly_data %}
                <div class="px-6 py-8 text-center text-gray-400">
                    <p class="text-lg">No volume anomalies detected</p>
                    <p class="text-sm mt-2">Waiting for significant trading activity...</p>
                </div>
                {% endif %}
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-800 border-t border-gray-700 mt-12">
        <div class="container mx-auto px-4 py-6">
            <div class="flex items-center justify-between">
                <p class="text-gray-400 text-sm">
                    Powered by QuickNode Streams & TimescaleDB
                </p>
                <div class="flex space-x-4 text-sm text-gray-400">
                    <a href="/api/swaps" class="hover:text-white transition-colors">API</a>
                    <a href="/health" class="hover:text-white transition-colors">Health</a>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Update time ago displays
        function updateTimeAgo() {
            document.querySelectorAll('.time-ago').forEach(element => {
                const timestamp = element.getAttribute('data-timestamp');
                if (timestamp) {
                    const date = new Date(timestamp);
                    const now = new Date();
                    const diffInSeconds = Math.floor((now - date) / 1000);

                    let timeAgo;
                    if (diffInSeconds < 60) {
                        timeAgo = `${diffInSeconds}s ago`;
                    } else if (diffInSeconds < 3600) {
                        timeAgo = `${Math.floor(diffInSeconds / 60)}m ago`;
                    } else {
                        timeAgo = `${Math.floor(diffInSeconds / 3600)}h ago`;
                    }

                    element.textContent = timeAgo;
                }
            });
        }

        // Update time displays every second
        setInterval(updateTimeAgo, 1000);

        // Initial update
        updateTimeAgo();

        // Handle stats updates
        document.body.addEventListener('htmx:afterRequest', function (event) {
            if (event.detail.pathInfo.requestPath === '/api/stats') {
                const response = JSON.parse(event.detail.xhr.responseText);
                document.getElementById('total-swaps').textContent = response.total_swaps;
                document.getElementById('unique-tokens').textContent = response.unique_tokens;
                document.getElementById('large-swaps-count').textContent = response.swaps_last_hour;
            }
        });

        // Add visual feedback for new anomalies
        document.body.addEventListener('htmx:afterSwap', function (event) {
            if (event.detail.target.id === 'anomalies-container') {
                // Add subtle animation to new anomaly cards
                const cards = event.detail.target.querySelectorAll('.bg-gray-800');
                cards.forEach((card, index) => {
                    if (index < 2) { // Highlight first 2 cards as "new"
                        card.classList.add('ring-2', 'ring-purple-500', 'ring-opacity-50');
                        setTimeout(() => {
                            card.classList.remove('ring-2', 'ring-purple-500', 'ring-opacity-50');
                        }, 3000);
                    }
                });

                // Update time ago for new content
                updateTimeAgo();
            }
        });
    </script>
</body>

</html>