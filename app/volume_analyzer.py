"""Volume anomaly detection and analysis service."""

import logging
from datetime import datetime, timezone, timedelta
from typing import List, Dict, Optional
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, desc
from app.models import Swap, Token, VolumeAnomaly, LargeSwap
from app.database import SessionLocal

logger = logging.getLogger(__name__)

class VolumeAnalyzer:
    """Service for detecting volume anomalies and analyzing trading patterns."""

    # Thresholds for significant trades (in lamports)
    LARGE_TRADE_THRESHOLD = 1_000_000_000  # 1 SOL
    WHALE_TRADE_THRESHOLD = 10_000_000_000  # 10 SOL

    # Minimum volume thresholds for anomaly detection (in SOL)
    MIN_VOLUME_1H = 0.1  # Minimum 0.1 SOL volume in 1h to be considered
    MIN_VOLUME_6H = 0.5  # Minimum 0.5 SOL volume in 6h to be considered
    MIN_VOLUME_24H = 1.0  # Minimum 1.0 SOL volume in 24h to be considered

    # Anomaly score thresholds
    MIN_ANOMALY_SCORE = 2.0  # Minimum 2x increase to be considered anomalous
    HIGH_ANOMALY_SCORE = 5.0  # 5x increase is highly anomalous

    def __init__(self):
        self.db = SessionLocal()

    def __del__(self):
        if hasattr(self, 'db'):
            self.db.close()

    def get_volume_anomalies(self, limit: int = 10) -> List[VolumeAnomaly]:
        """Get tokens with volume anomalies, sorted by anomaly score."""
        try:
            now = datetime.now(timezone.utc)

            # Time windows
            hour_ago = now - timedelta(hours=1)
            six_hours_ago = now - timedelta(hours=6)
            day_ago = now - timedelta(hours=24)
            week_ago = now - timedelta(days=7)

            # Get all tokens with recent activity
            active_tokens = self.db.query(Token.mint_address).join(
                Swap, Token.mint_address == Swap.mint_address
            ).filter(Swap.timestamp >= day_ago).distinct().all()

            anomalies = []

            for token_row in active_tokens:
                mint_address = token_row.mint_address
                anomaly = self._calculate_token_anomaly(
                    mint_address, now, hour_ago, six_hours_ago, day_ago, week_ago
                )

                if anomaly and anomaly.overall_anomaly_score >= self.MIN_ANOMALY_SCORE:
                    anomalies.append(anomaly)

            # Sort by overall anomaly score (highest first)
            anomalies.sort(key=lambda x: x.overall_anomaly_score, reverse=True)

            return anomalies[:limit]

        except Exception as e:
            logger.error(f"Error getting volume anomalies: {e}")
            return []

    def _calculate_token_anomaly(
        self,
        mint_address: str,
        now: datetime,
        hour_ago: datetime,
        six_hours_ago: datetime,
        day_ago: datetime,
        week_ago: datetime
    ) -> Optional[VolumeAnomaly]:
        """Calculate volume anomaly for a specific token."""
        try:
            # Get token info
            token = self.db.query(Token).filter(Token.mint_address == mint_address).first()
            if not token:
                return None

            # Calculate current volume metrics
            volume_1h = self._get_volume_in_period(mint_address, hour_ago, now)
            volume_6h = self._get_volume_in_period(mint_address, six_hours_ago, now)
            volume_24h = self._get_volume_in_period(mint_address, day_ago, now)

            # Skip if volume is too low
            if volume_1h < self.MIN_VOLUME_1H and volume_6h < self.MIN_VOLUME_6H and volume_24h < self.MIN_VOLUME_24H:
                return None

            # Calculate historical averages (last 7 days, excluding current period)
            avg_volume_1h = self._get_average_hourly_volume(mint_address, week_ago, day_ago)
            avg_volume_6h = self._get_average_6h_volume(mint_address, week_ago, day_ago)
            avg_volume_24h = self._get_average_daily_volume(mint_address, week_ago, day_ago)

            # Calculate anomaly scores
            anomaly_score_1h = volume_1h / avg_volume_1h if avg_volume_1h > 0 else 0
            anomaly_score_6h = volume_6h / avg_volume_6h if avg_volume_6h > 0 else 0
            anomaly_score_24h = volume_24h / avg_volume_24h if avg_volume_24h > 0 else 0

            # Calculate overall anomaly score (weighted combination)
            # Give more weight to recent activity
            overall_score = (
                anomaly_score_1h * 0.5 +  # 50% weight on 1h
                anomaly_score_6h * 0.3 +  # 30% weight on 6h
                anomaly_score_24h * 0.2   # 20% weight on 24h
            )

            # Get additional metrics
            swap_count_1h = self._get_swap_count_in_period(mint_address, hour_ago, now)
            swap_count_6h = self._get_swap_count_in_period(mint_address, six_hours_ago, now)
            swap_count_24h = self._get_swap_count_in_period(mint_address, day_ago, now)

            largest_swap_1h = self._get_largest_swap_in_period(mint_address, hour_ago, now)
            last_activity = self._get_last_activity(mint_address)

            return VolumeAnomaly(
                mint_address=mint_address,
                token_name=token.name,
                token_symbol=token.symbol,
                volume_1h=volume_1h,
                volume_6h=volume_6h,
                volume_24h=volume_24h,
                avg_volume_1h=avg_volume_1h,
                avg_volume_6h=avg_volume_6h,
                avg_volume_24h=avg_volume_24h,
                anomaly_score_1h=anomaly_score_1h,
                anomaly_score_6h=anomaly_score_6h,
                anomaly_score_24h=anomaly_score_24h,
                overall_anomaly_score=overall_score,
                swap_count_1h=swap_count_1h,
                swap_count_6h=swap_count_6h,
                swap_count_24h=swap_count_24h,
                largest_swap_1h=largest_swap_1h,
                last_activity=last_activity
            )

        except Exception as e:
            logger.error(f"Error calculating anomaly for {mint_address}: {e}")
            return None

    def _get_volume_in_period(self, mint_address: str, start: datetime, end: datetime) -> float:
        """Get total SOL volume for a token in a time period."""
        result = self.db.query(func.sum(Swap.sol_amount)).filter(
            and_(
                Swap.mint_address == mint_address,
                Swap.timestamp >= start,
                Swap.timestamp < end
            )
        ).scalar()

        return float(result or 0) / 1_000_000_000  # Convert lamports to SOL

    def _get_swap_count_in_period(self, mint_address: str, start: datetime, end: datetime) -> int:
        """Get number of swaps for a token in a time period."""
        result = self.db.query(func.count(Swap.id)).filter(
            and_(
                Swap.mint_address == mint_address,
                Swap.timestamp >= start,
                Swap.timestamp < end
            )
        ).scalar()

        return result or 0

    def _get_largest_swap_in_period(self, mint_address: str, start: datetime, end: datetime) -> float:
        """Get the largest single swap for a token in a time period."""
        result = self.db.query(func.max(Swap.sol_amount)).filter(
            and_(
                Swap.mint_address == mint_address,
                Swap.timestamp >= start,
                Swap.timestamp < end
            )
        ).scalar()

        return float(result or 0) / 1_000_000_000  # Convert lamports to SOL

    def _get_last_activity(self, mint_address: str) -> Optional[datetime]:
        """Get the timestamp of the last swap for a token."""
        result = self.db.query(func.max(Swap.timestamp)).filter(
            Swap.mint_address == mint_address
        ).scalar()

        return result

    def _get_average_hourly_volume(self, mint_address: str, start: datetime, end: datetime) -> float:
        """Get average hourly volume over a period."""
        total_volume = self._get_volume_in_period(mint_address, start, end)
        hours = (end - start).total_seconds() / 3600
        return total_volume / hours if hours > 0 else 0

    def _get_average_6h_volume(self, mint_address: str, start: datetime, end: datetime) -> float:
        """Get average 6-hour volume over a period."""
        total_volume = self._get_volume_in_period(mint_address, start, end)
        periods = (end - start).total_seconds() / (6 * 3600)
        return total_volume / periods if periods > 0 else 0

    def _get_average_daily_volume(self, mint_address: str, start: datetime, end: datetime) -> float:
        """Get average daily volume over a period."""
        total_volume = self._get_volume_in_period(mint_address, start, end)
        days = (end - start).total_seconds() / (24 * 3600)
        return total_volume / days if days > 0 else 0

    def get_large_swaps_for_token(self, mint_address: str, limit: int = 5) -> List[LargeSwap]:
        """Get recent large swaps for a specific token."""
        try:
            now = datetime.now(timezone.utc)
            hour_ago = now - timedelta(hours=1)

            # Get recent large swaps for this token
            swaps_query = self.db.query(
                Swap.signature,
                Swap.timestamp,
                Swap.mint_address,
                Swap.token_amount,
                Swap.sol_amount,
                Swap.price_per_token,
                Swap.swap_type,
                Swap.user_wallet,
                Token.name.label('token_name'),
                Token.symbol.label('token_symbol'),
                Token.decimals.label('token_decimals')
            ).join(Token, Swap.mint_address == Token.mint_address).filter(
                and_(
                    Swap.mint_address == mint_address,
                    Swap.sol_amount >= self.LARGE_TRADE_THRESHOLD,  # Only large trades
                    Swap.timestamp >= hour_ago  # Only recent trades
                )
            ).order_by(desc(Swap.timestamp)).limit(limit).all()

            large_swaps = []
            for swap in swaps_query:
                sol_display = swap.sol_amount / 1_000_000_000
                token_display = swap.token_amount / (10 ** swap.token_decimals)
                seconds_ago = (now - swap.timestamp).total_seconds()

                large_swaps.append(LargeSwap(
                    signature=swap.signature,
                    mint_address=swap.mint_address,
                    token_name=swap.token_name,
                    token_symbol=swap.token_symbol,
                    token_decimals=swap.token_decimals,
                    timestamp=swap.timestamp,
                    sol_amount=swap.sol_amount,
                    token_amount=swap.token_amount,
                    sol_display=sol_display,
                    token_display=token_display,
                    price_per_token=swap.price_per_token,
                    swap_type=swap.swap_type,
                    user_wallet=swap.user_wallet,
                    is_whale_trade=swap.sol_amount >= self.WHALE_TRADE_THRESHOLD,
                    is_large_trade=swap.sol_amount >= self.LARGE_TRADE_THRESHOLD,
                    seconds_ago=seconds_ago
                ))

            return large_swaps

        except Exception as e:
            logger.error(f"Error getting large swaps for {mint_address}: {e}")
            return []
