#!/usr/bin/env python3
"""Test script to create sample data for volume anomaly detection."""

import os
import sys
from datetime import datetime, timezone, timedelta
import random

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.database import SessionLocal
from app.models import Token, Swap
from app.volume_analyzer import VolumeAnalyzer

def create_test_data():
    """Create test data with volume anomalies."""
    print("Creating test data for volume anomaly detection...")
    
    db = SessionLocal()
    try:
        # Create test tokens
        test_tokens = [
            {
                'mint_address': 'TestToken1111111111111111111111111111111',
                'name': 'Test Meme Coin',
                'symbol': 'MEME',
                'decimals': 6
            },
            {
                'mint_address': 'TestToken2222222222222222222222222222222',
                'name': 'Pump Token',
                'symbol': 'PUMP',
                'decimals': 6
            },
            {
                'mint_address': 'TestToken3333333333333333333333333333333',
                'name': 'Volume Spike',
                'symbol': 'SPIKE',
                'decimals': 6
            }
        ]
        
        # Create tokens
        for token_data in test_tokens:
            existing = db.query(Token).filter(Token.mint_address == token_data['mint_address']).first()
            if not existing:
                token = Token(**token_data)
                db.add(token)
        
        db.commit()
        print("✅ Created test tokens")
        
        # Create historical data (7 days ago to 2 days ago) - normal volume
        now = datetime.now(timezone.utc)
        
        # Historical baseline data
        for days_back in range(7, 2, -1):
            for token_data in test_tokens:
                mint_address = token_data['mint_address']
                
                # Create 5-10 small swaps per day for baseline
                num_swaps = random.randint(5, 10)
                for i in range(num_swaps):
                    timestamp = now - timedelta(days=days_back) + timedelta(hours=random.randint(0, 23))
                    
                    # Small swaps (0.01 to 0.5 SOL)
                    sol_amount = random.randint(10_000_000, 500_000_000)  # 0.01 to 0.5 SOL in lamports
                    token_amount = random.randint(1_000_000, 100_000_000)  # 1 to 100 tokens
                    price_per_token = sol_amount / token_amount / 1_000_000_000
                    
                    swap = Swap(
                        signature=f"baseline_{mint_address}_{days_back}_{i}",
                        timestamp=timestamp,
                        mint_address=mint_address,
                        token_amount=token_amount,
                        sol_amount=sol_amount,
                        price_per_token=price_per_token,
                        swap_type=random.choice(['buy', 'sell']),
                        user_wallet=f"User{random.randint(1000, 9999)}"
                    )
                    db.add(swap)
        
        db.commit()
        print("✅ Created historical baseline data")
        
        # Create recent anomalous activity (last 2 hours)
        recent_time = now - timedelta(hours=2)
        
        # Token 1: High volume anomaly with large trades
        mint_address = test_tokens[0]['mint_address']
        for i in range(15):  # Many trades
            timestamp = recent_time + timedelta(minutes=random.randint(0, 120))
            
            # Mix of large and medium trades
            if i < 5:  # Large trades (whale activity)
                sol_amount = random.randint(10_000_000_000, 50_000_000_000)  # 10-50 SOL
            elif i < 10:  # Medium trades
                sol_amount = random.randint(1_000_000_000, 5_000_000_000)  # 1-5 SOL
            else:  # Small trades
                sol_amount = random.randint(100_000_000, 1_000_000_000)  # 0.1-1 SOL
            
            token_amount = random.randint(10_000_000, 1_000_000_000)
            price_per_token = sol_amount / token_amount / 1_000_000_000
            
            swap = Swap(
                signature=f"anomaly1_{i}_{timestamp.timestamp()}",
                timestamp=timestamp,
                mint_address=mint_address,
                token_amount=token_amount,
                sol_amount=sol_amount,
                price_per_token=price_per_token,
                swap_type=random.choice(['buy', 'sell']),
                user_wallet=f"Whale{random.randint(1, 10)}"
            )
            db.add(swap)
        
        # Token 2: Medium volume spike
        mint_address = test_tokens[1]['mint_address']
        for i in range(8):
            timestamp = recent_time + timedelta(minutes=random.randint(0, 120))
            
            # Medium to large trades
            sol_amount = random.randint(2_000_000_000, 15_000_000_000)  # 2-15 SOL
            token_amount = random.randint(50_000_000, 500_000_000)
            price_per_token = sol_amount / token_amount / 1_000_000_000
            
            swap = Swap(
                signature=f"anomaly2_{i}_{timestamp.timestamp()}",
                timestamp=timestamp,
                mint_address=mint_address,
                token_amount=token_amount,
                sol_amount=sol_amount,
                price_per_token=price_per_token,
                swap_type=random.choice(['buy', 'sell']),
                user_wallet=f"Trader{random.randint(100, 999)}"
            )
            db.add(swap)
        
        # Token 3: Recent whale activity
        mint_address = test_tokens[2]['mint_address']
        for i in range(3):
            timestamp = recent_time + timedelta(minutes=random.randint(0, 60))  # Last hour
            
            # Very large trades (whale activity)
            sol_amount = random.randint(25_000_000_000, 100_000_000_000)  # 25-100 SOL
            token_amount = random.randint(100_000_000, 2_000_000_000)
            price_per_token = sol_amount / token_amount / 1_000_000_000
            
            swap = Swap(
                signature=f"whale_{i}_{timestamp.timestamp()}",
                timestamp=timestamp,
                mint_address=mint_address,
                token_amount=token_amount,
                sol_amount=sol_amount,
                price_per_token=price_per_token,
                swap_type=random.choice(['buy', 'sell']),
                user_wallet=f"MegaWhale{i}"
            )
            db.add(swap)
        
        db.commit()
        print("✅ Created anomalous trading data")
        
        # Test the volume analyzer
        print("\n🔍 Testing volume anomaly detection...")
        analyzer = VolumeAnalyzer()
        anomalies = analyzer.get_volume_anomalies(limit=5)
        
        print(f"\n📊 Found {len(anomalies)} volume anomalies:")
        for anomaly in anomalies:
            print(f"  • {anomaly.token_symbol or anomaly.mint_address[:8]}...")
            print(f"    Anomaly Score: {anomaly.overall_anomaly_score:.1f}x")
            print(f"    1h Volume: {anomaly.volume_1h:.3f} SOL")
            print(f"    6h Volume: {anomaly.volume_6h:.3f} SOL")
            print(f"    24h Volume: {anomaly.volume_24h:.3f} SOL")
            print(f"    Swaps (1h): {anomaly.swap_count_1h}")
            
            # Get large swaps for this token
            large_swaps = analyzer.get_large_swaps_for_token(anomaly.mint_address, limit=3)
            if large_swaps:
                print(f"    Large Trades:")
                for swap in large_swaps:
                    print(f"      - {swap.swap_type.title()}: {swap.sol_display:.3f} SOL {'🐋' if swap.is_whale_trade else ''}")
            print()
        
        print("🎉 Test data created successfully!")
        print("🌐 Visit http://localhost:8000 to see the volume anomalies in action!")
        
    except Exception as e:
        print(f"❌ Error creating test data: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    create_test_data()
